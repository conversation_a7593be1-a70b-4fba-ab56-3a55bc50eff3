import { useDispatch, useSelector, TypedUseSelectorHook } from "react-redux";
import { useCallback } from "react";
import type { RootState, AppDispatch } from "./index";
import {
  setSearchQuery,
  setSearchCategory,
  setSearchLocation,
  clearSearch,
  performSearch,
} from "./slices/searchSlice";
import { selectCategory, toggleFilters } from "./slices/categorySlice";
import {
  setActiveFilters,
  updateSingleFilter,
  clearFilters,
  updateAvailableFilters,
} from "./slices/filterSlice";
import {
  setSortBy,
  setCurrentPage,
  setItemsPerPage,
  resetPagination,
} from "./slices/productSlice";
import {
  addToCart,
  removeFromCart,
  updateCartQuantity,
  clearCart,
} from "./slices/cartSlice";
import {
  setCheckoutStep,
  setSelectedAddress,
  setSelectedPaymentMethod,
  resetCheckout,
} from "./slices/orderSlice";
import {
  clearTransactionsError,
  clearWalletError,
  resetTransactions,
} from "./slices/paymentSlice";
import { openModal, closeModal } from "./slices/modalSlice";

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Convenience hooks for common operations
export const useTypedSelector = useSelector.withTypes<RootState>();
export const useTypedDispatch = useDispatch.withTypes<AppDispatch>();

// Custom hooks for common Redux operations (similar to the original context actions)
export const useEcommerceActions = () => {
  const dispatch = useAppDispatch();

  // Search actions
  const setSearch = useCallback(
    (query: string) => {
      dispatch(setSearchQuery(query));
    },
    [dispatch]
  );

  const setSearchCat = useCallback(
    (category: string) => {
      dispatch(setSearchCategory(category));
    },
    [dispatch]
  );

  const setSearchLoc = useCallback(
    (location: string) => {
      dispatch(setSearchLocation(location));
    },
    [dispatch]
  );

  const performSearchAction = useCallback(
    (params: { query: string; category?: string; location?: string }) => {
      dispatch(performSearch(params));
    },
    [dispatch]
  );

  const clearSearchAction = useCallback(() => {
    dispatch(clearSearch());
  }, [dispatch]);

  // Category actions
  const selectCategoryAction = useCallback(
    (categoryId: string | null) => {
      dispatch(selectCategory(categoryId));
    },
    [dispatch]
  );

  const toggleFiltersAction = useCallback(
    (show: boolean) => {
      dispatch(toggleFilters(show));
    },
    [dispatch]
  );

  // Filter actions
  const setActiveFiltersAction = useCallback(
    (filters: any) => {
      dispatch(setActiveFilters(filters));
    },
    [dispatch]
  );

  const updateSingleFilterAction = useCallback(
    (key: string, value: any) => {
      dispatch(updateSingleFilter({ key, value }));
    },
    [dispatch]
  );

  const clearFiltersAction = useCallback(() => {
    dispatch(clearFilters());
  }, [dispatch]);

  const updateAvailableFiltersAction = useCallback(
    (categoryId: string | null) => {
      dispatch(updateAvailableFilters(categoryId));
    },
    [dispatch]
  );

  // Product UI actions (simplified for API-driven approach)
  const setSortByAction = useCallback(
    (sortBy: any) => {
      dispatch(setSortBy(sortBy));
    },
    [dispatch]
  );

  const setCurrentPageAction = useCallback(
    (page: number) => {
      dispatch(setCurrentPage(page));
    },
    [dispatch]
  );

  const setItemsPerPageAction = useCallback(
    (itemsPerPage: number) => {
      dispatch(setItemsPerPage(itemsPerPage));
    },
    [dispatch]
  );

  const resetPaginationAction = useCallback(() => {
    dispatch(resetPagination());
  }, [dispatch]);

  // Cart actions
  const addToCartAction = useCallback(
    (product: any, quantity: number) => {
      dispatch(addToCart({ product, quantity }));
    },
    [dispatch]
  );

  const removeFromCartAction = useCallback(
    (productId: string) => {
      dispatch(removeFromCart(productId));
    },
    [dispatch]
  );

  const updateCartQuantityAction = useCallback(
    (productId: string, quantity: number) => {
      dispatch(updateCartQuantity({ productId, quantity }));
    },
    [dispatch]
  );

  const clearCartAction = useCallback(() => {
    dispatch(clearCart());
  }, [dispatch]);

  // API-integrated cart actions
  const fetchCartAction = useCallback(async () => {
    return dispatch({ type: "cart/fetchCart" });
  }, [dispatch]);

  const addProductToCartAction = useCallback(
    async (product: any, quantity: number) => {
      return dispatch({
        type: "cart/addProductToCart",
        payload: { product, quantity },
      });
    },
    [dispatch]
  );

  const updateCartItemAction = useCallback(
    async (itemId: string, quantity: number) => {
      return dispatch({
        type: "cart/updateCartItemAsync",
        payload: { itemId, data: { quantity } },
      });
    },
    [dispatch]
  );

  const removeCartItemAction = useCallback(
    async (itemId: string) => {
      return dispatch({ type: "cart/removeCartItemAsync", payload: itemId });
    },
    [dispatch]
  );

  const clearCartAsyncAction = useCallback(async () => {
    return dispatch({ type: "cart/clearCartAsync" });
  }, [dispatch]);

  const syncCartAction = useCallback(async () => {
    return dispatch({ type: "cart/syncCartWithServer" });
  }, [dispatch]);

  const bulkRemoveCartItemsAction = useCallback(
    async (itemIds: string[]) => {
      return dispatch({
        type: "cart/bulkRemoveCartItemsAsync",
        payload: { itemIds },
      });
    },
    [dispatch]
  );

  // Modal actions
  const openModalAction = useCallback(
    (content: any, props?: any, type?: string) => {
      dispatch(openModal({ content, props, type }));
    },
    [dispatch]
  );

  const closeModalAction = useCallback(() => {
    dispatch(closeModal());
  }, [dispatch]);

  // Order actions
  const setCheckoutStepAction = useCallback(
    (step: any) => {
      dispatch(setCheckoutStep(step));
    },
    [dispatch]
  );

  const setSelectedAddressAction = useCallback(
    (address: any) => {
      dispatch(setSelectedAddress(address));
    },
    [dispatch]
  );

  const setSelectedPaymentMethodAction = useCallback(
    (method: any) => {
      dispatch(setSelectedPaymentMethod(method));
    },
    [dispatch]
  );

  const resetCheckoutAction = useCallback(() => {
    dispatch(resetCheckout());
  }, [dispatch]);

  // Payment actions
  const clearTransactionsErrorAction = useCallback(() => {
    dispatch(clearTransactionsError());
  }, [dispatch]);

  const clearWalletErrorAction = useCallback(() => {
    dispatch(clearWalletError());
  }, [dispatch]);

  const resetTransactionsAction = useCallback(() => {
    dispatch(resetTransactions());
  }, [dispatch]);

  return {
    // Search
    setSearchQuery: setSearch,
    setSearchCategory: setSearchCat,
    setSearchLocation: setSearchLoc,
    performSearch: performSearchAction,
    clearSearch: clearSearchAction,

    // Category
    selectCategory: selectCategoryAction,
    toggleFilters: toggleFiltersAction,

    // Filter
    setActiveFilters: setActiveFiltersAction,
    updateSingleFilter: updateSingleFilterAction,
    clearFilters: clearFiltersAction,
    updateAvailableFilters: updateAvailableFiltersAction,

    // Product UI (simplified for API-driven approach)
    setSortBy: setSortByAction,
    setCurrentPage: setCurrentPageAction,
    setItemsPerPage: setItemsPerPageAction,
    resetPagination: resetPaginationAction,

    // Cart (local state)
    addToCart: addToCartAction,
    removeFromCart: removeFromCartAction,
    updateCartQuantity: updateCartQuantityAction,
    clearCart: clearCartAction,

    // Cart (API-integrated)
    fetchCart: fetchCartAction,
    addProductToCart: addProductToCartAction,
    updateCartItem: updateCartItemAction,
    removeCartItem: removeCartItemAction,
    clearCartAsync: clearCartAsyncAction,
    syncCart: syncCartAction,
    bulkRemoveCartItems: bulkRemoveCartItemsAction,

    // Modal
    openModal: openModalAction,
    closeModal: closeModalAction,

    // Order
    setCheckoutStep: setCheckoutStepAction,
    setSelectedAddress: setSelectedAddressAction,
    setSelectedPaymentMethod: setSelectedPaymentMethodAction,
    resetCheckout: resetCheckoutAction,

    // Payment
    clearTransactionsError: clearTransactionsErrorAction,
    clearWalletError: clearWalletErrorAction,
    resetTransactions: resetTransactionsAction,
  };
};
