"use client";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Icon } from "@iconify/react";
import { useEcommerceActions } from "@/store/hooks";
import type { Product } from "@/types/ecommerce";
import { AddToCartModal } from "@/components/features/cart";
import { useRouter } from "next/navigation";
import { useModal } from "@/store/compatibility";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";

interface PurchaseProps {
  basePrice: number;
  currency?: string;
  product?: Product;
}

export function Purchase({
  basePrice,
  currency = "Rs.",
  product,
}: PurchaseProps) {
  const [quantity, setQuantity] = useState(1);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const { addProductToCart } = useEcommerceActions();
  const router = useRouter();
  const { openModal } = useModal();

  const handleQuantityChange = (value: string) => {
    const num = Number.parseInt(value) || 1;
    setQuantity(Math.max(1, num));
  };

  const incrementQuantity = () => {
    setQuantity((prev) => prev + 1);
  };

  const decrementQuantity = () => {
    setQuantity((prev) => Math.max(1, prev - 1));
  };

  const handleAddToCart = async () => {
    if (product && !isAddingToCart) {
      setIsAddingToCart(true);
      try {
        await addProductToCart(product, quantity);
        openModal(
          <AddToCartModal
            product={product}
            quantity={quantity}
            onViewCart={handleViewCart}
          />
        );
      } catch (error) {
        console.error("Failed to add product to cart:", error);
        // You could show an error toast here
      } finally {
        setIsAddingToCart(false);
      }
    }
  };

  const handleViewCart = () => {
    router.push("/cart");
  };

  const totalPrice = basePrice * quantity;

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <h3 className="text-xl font-medium text-gray-900 mb-6">Purchase</h3>

      {/* Quantity Selector */}
      <div className="mb-6">
        <label className="text-sm font-medium text-gray-900 mb-3 block">
          Quantity
        </label>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={decrementQuantity}
            className="h-8 w-8 p-0 border-gray-300 bg-transparent"
            disabled={quantity <= 1}
          >
            <Icon icon="lucide:minus" className="h-4 w-4" />
          </Button>
          <Input
            type="number"
            value={quantity}
            onChange={(e) => handleQuantityChange(e.target.value)}
            className="w-16 h-8 text-center border-gray-300"
            min="1"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={incrementQuantity}
            className="h-8 w-8 p-0 border-gray-300 bg-transparent"
          >
            <Icon icon="lucide:plus" className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Price Details */}
      <div className="space-y-3  rounded-lg  mr-2 p-2 mb-4">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Base Price (per unit):</span>
          <span className="text-gray-900">
            {currency}
            {basePrice.toLocaleString()}
          </span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Quantity</span>
          <span className="text-gray-900">x {quantity}</span>
        </div>

        <div className="border-t border-gray-200 pt-3">
          <div className="flex justify-between items-center">
            <span className="text-base font-medium text-gray-900">
              Total Price:
            </span>
            <span className="text-lg font-semibold text-teal-600">
              {currency} {totalPrice.toLocaleString()}
            </span>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="space-y-3">
        <Button
          variant="outline"
          onClick={handleAddToCart}
          disabled={isAddingToCart}
          className="w-full text-teal-600 border-teal-600 hover:bg-teal-50 bg-transparent disabled:opacity-50"
        >
          {isAddingToCart ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Adding...
            </>
          ) : (
            <>
              <Icon icon="lucide:shopping-cart" className="h-4 w-4 mr-2" />
              Add To Cart
            </>
          )}
        </Button>
        <Button className="w-full bg-teal-600 hover:bg-teal-700 text-white">
          <Icon icon="lucide:credit-card" className="h-4 w-4 mr-2" />
          Buy Now
        </Button>
      </div>
    </div>
  );
}
